import 'package:flick_video_player/flick_video_player.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/conversation_controller.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/conversation_state.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/main_lesson_app_bar.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';
import 'package:video_player/video_player.dart';

class ConversationVideoScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  final String path;

  const ConversationVideoScreen({
    super.key,
    required this.level,
    required this.chapter,
    required this.path,
  });

  @override
  ConsumerState<ConversationVideoScreen> createState() =>
      _ConversationVideoScreenState();
}

class _ConversationVideoScreenState
    extends ConsumerState<ConversationVideoScreen> {
  late AsyncValue<ConversationState> viewState;
  late ConversationController viewModel;
  late final ScrollController _scrollController;
  FlickManager? _flickManager;

  static const _tmpVideo =
      "https://firebasestorage.googleapis.com/v0/b/selfeng-dev.appspot.com/o/video%2Ftmp%2Ftmpvideo.mp4?alt=media&token=423dc215-6ccf-4c16-9971-a6306f998f10";

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _initializeDefaultVideo();
  }

  Future<void> _initializeDefaultVideo() async {
    try {
      final controller = VideoPlayerController.networkUrl(Uri.parse(_tmpVideo));
      await controller.initialize();
      setState(() {
        _flickManager = FlickManager(videoPlayerController: controller);
      });
    } catch (e) {
      _showError(
        title: 'Video Initialization Failed',
        message:
            'Unable to load the video player. Please check your internet connection and try again.',
      );
    }
  }

  @override
  void dispose() {
    try {
      // Dispose FlickManager
      _flickManager?.dispose();

      // Dispose all video controllers from conversations
      // final conversations = viewState.value?.conversations;
      // if (conversations != null) {
      //   for (final conversation in conversations) {
      //     conversation.videoController.dispose();
      //   }
      // }
      viewState.value?.flickManager?.dispose();
      // Dispose scroll controller
      _scrollController.dispose();
    } catch (e) {
      // No need to show error on dispose
    } finally {
      super.dispose();
    }
  }

  void _showError({
    required String title,
    required String message,
    Duration duration = const Duration(seconds: 4),
    bool isFloating = true,
  }) {
    if (!mounted) return;

    final theme = Theme.of(context);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        behavior:
            isFloating ? SnackBarBehavior.floating : SnackBarBehavior.fixed,
        duration: duration,
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: theme.textTheme.titleSmall?.copyWith(
                color: theme.colorScheme.onError,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onError,
              ),
            ),
          ],
        ),
        backgroundColor: theme.colorScheme.error,
        action: SnackBarAction(
          label: 'Dismiss',
          textColor: theme.colorScheme.onError,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  Future<void> _handleNextVideo() async {
    final currentState = viewState.value;
    if (currentState == null) return;

    final nextIndex = currentState.selectedIndex + 1;
    if (nextIndex >= currentState.conversations.length) {
      customNav(
        context,
        RouterName.conversationVideoResult,
        isReplace: true,
        params: {'level': widget.level, 'chapter': widget.chapter},
      );
    }

    try {
      await viewModel.saveResult();
      await viewModel.selectVideo(nextIndex);
    } catch (e) {
      _showError(
        title: 'Navigation Error',
        message: 'Failed to move to the next video. Please try again.',
      );
    }
  }

  Future<void> _handlePreviousVideo() async {
    final currentState = viewState.value;
    if (currentState == null) return;

    if (currentState.selectedIndex > 0) {
      try {
        await viewModel.selectVideo(currentState.selectedIndex - 1);
      } catch (e) {
        _showError(
          title: 'Navigation Error',
          message: 'Failed to move to the previous video. Please try again.',
        );
      }
    } else {
      _showError(
        title: 'Navigation Error',
        message: 'You are already at the first video.',
        duration: const Duration(seconds: 2),
      );
    }
  }

  Future<void> _retryContent() async {
    try {
      await viewModel.initContent();
    } catch (e) {
      _showError(
        title: 'Initialization Failed',
        message:
            'Unable to load the content. Please check your connection and try again.',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final prov = conversationControllerProvider(
      widget.level,
      widget.chapter,
      widget.path,
      context.loc.localeName,
    );
    viewState = ref.watch(prov);
    viewModel = ref.watch(prov.notifier);

    ref.listen(prov.select((value) => value), ((previous, next) {
      next.maybeWhen(
        error:
            (error, _) => _showError(title: 'Error', message: error.toString()),
        orElse: () {},
      );
    }));

    return switch (viewState) {
      AsyncData() => _buildBody(),
      AsyncError(:final error) => _buildErrorView(error),
      _ => const AppLoading(),
    };
  }

  Widget _buildErrorView(Object error) {
    return Scaffold(
      body: Center(
        child: Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.errorContainer,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.error_outline,
                size: 48,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                'Something went wrong',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onErrorContainer,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onErrorContainer,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _retryContent,
                icon: const Icon(Icons.refresh),
                label: const Text('Try Again'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBody() {
    return Scaffold(
      appBar: MainLessonAppBar(
        title: context.loc.conversationVideo,
        isBookmarked:
            viewState.value!.conversations.isNotEmpty
                ? viewState
                    .value!
                    .conversations[viewState.value!.selectedIndex]
                    .isBookmarked
                : false,
        onBookmark: () {
          viewModel.saveBookmark();
        },
        onHelp: () {
          customNav(
            context,
            RouterName.conversationVideoInstruction,
            isReplace: true,
            params: {'level': widget.level, 'chapter': widget.chapter},
          );
        },
      ),
      body: _buildMainContent(),
    );
  }

  Widget _buildMainContent() {
    return ListView(
      controller: _scrollController,
      physics: const BouncingScrollPhysics(),
      children: [
        const SizedBox(height: 24),
        if ((viewState.value?.conversations.isNotEmpty ?? false) &&
            viewState.value != null)
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 65),
            child: LinearProgressIndicator(
              value:
                  viewState.value!.selectedIndex +
                  1 / viewState.value!.conversations.length,
              backgroundColor: const Color(0xffFFDAD2),
            ),
          ),
        const SizedBox(height: 24),
        _buildVideoPlayer(),
        const SizedBox(height: 32),
        if ((viewState.value?.conversations.isNotEmpty ?? false) &&
            viewState.value != null)
          _buildConversationInfo(),
        const SizedBox(height: 80),
      ],
    );
  }

  Widget _buildVideoPlayer() {
    final currentState = viewState.value;
    debugPrint(
      'subtitle ${(currentState?.flickManager)?.flickVideoManager?.videoPlayerController?.value.caption.text}',
    );
    return Container(
      width: double.infinity,
      height: MediaQuery.of(context).size.height / 1.4,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        gradient: const LinearGradient(
          colors: [Color(0xffFE754C), Color(0xffE21F29), Color(0xffC3151F)],
          begin: Alignment.bottomLeft,
          end: Alignment.topRight,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: FlickVideoPlayer(
          flickVideoWithControls: FlickVideoWithControls(
            videoFit: BoxFit.cover,
            controls: FlickPortraitControls(fontSize: 12, iconSize: 25),
            closedCaptionTextStyle:
                Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(color: Colors.white) ??
                const TextStyle(),
          ),
          flickManager: currentState?.flickManager ?? _flickManager!,
        ),
      ),
    );
  }

  Widget _buildConversationInfo() {
    final currentState = viewState.value!;
    final currentConversation =
        currentState.conversations[currentState.selectedIndex];

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.symmetric(vertical: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: const Color(0xffF8F8F8).withValues(alpha: .4),
        border: Border.all(color: Colors.white, width: 0.8),
      ),
      child: _buildVideoControls(
        title: currentConversation.title ?? '',
        videoController: currentConversation.videoController,
        duration: currentConversation.videoMeta?.duration ?? Duration.zero,
      ),
    );
  }

  Widget _buildVideoControls({
    required String title,
    required VideoPlayerController videoController,
    required Duration duration,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildControlButton(
          icon: Icons.skip_previous,
          onTap: _handlePreviousVideo,
          margin: const EdgeInsets.only(left: 24, right: 8),
        ),
        SizedBox(
          width: MediaQuery.of(context).size.width * 0.56,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: const Color(0xffC00017),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              Text(
                _formatDuration(duration),
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: const Color(0xff7F7573)),
              ),
            ],
          ),
        ),
        _buildControlButton(
          icon: Icons.skip_next,
          onTap: _handleNextVideo,
          margin: const EdgeInsets.only(right: 24),
        ),
      ],
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onTap,
    required EdgeInsets margin,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
          color: Color(0xffA90013),
        ),
        padding: const EdgeInsets.all(5),
        margin: margin,
        child: Icon(icon, color: Colors.white),
      ),
    );
  }
}
