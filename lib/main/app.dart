import 'dart:ui';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router.dart';
import 'package:selfeng/features/setting/presentation/providers/setting_controller.dart';
import 'package:selfeng/l10n/generated/app_localizations.dart';
import 'package:selfeng/main/app_env.dart';
import 'package:selfeng/services/notification_service/domain/providers/notification_service_provider.dart';
import 'package:selfeng/services/in_app_update_service/presentation/controllers/in_app_update_controller.dart';
import 'package:selfeng/shared/theme/app_theme.dart';

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  // final appRouter = AppRouter();

  /// Initialize notification service after user authentication
  void _initializeNotificationAfterAuth(WidgetRef ref) {
    Future.microtask(() async {
      try {
        final notificationService = ref.read(notificationServiceProvider);

        // Complete full initialization now that user is authenticated
        final result = await notificationService.completeInitialization();
        result.fold((error) => null, (_) => null);
      } catch (e) {
        // Handle exception silently
      }
    });
  }

  /// Initialize in-app update service after user authentication
  void _initializeUpdateAfterAuth(WidgetRef ref) {
    Future.microtask(() async {
      try {
        final updateController = ref.read(
          inAppUpdateControllerProvider.notifier,
        );

        // Initialize and check for updates
        await updateController.initialize();

        // Check if we should start native update flow
        final updateState = ref.read(inAppUpdateControllerProvider);
        if (updateState.isUpdateAvailable) {
          // Start native Android update flow
          if (updateController.shouldForceImmediateUpdate) {
            // For critical updates, use immediate update (native full-screen dialog)
            await updateController.startImmediateUpdate();
          } else {
            // For non-critical updates, use flexible update (native notification)
            await updateController.startFlexibleUpdate();
          }
        }
      } catch (e) {
        // Handle exception silently
      }
    });
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(appThemeProvider);
    final settingState = ref.watch(settingControllerProvider);
    final router = ref.watch(routerProvider);

    // Notification service is now initialized in main.dart during app startup
    DartPluginRegistrant.ensureInitialized();

    FirebaseAuth.instance.authStateChanges().listen((User? user) {
      router.refresh();

      // Initialize services after authentication state changes
      if (user != null) {
        _initializeNotificationAfterAuth(ref);
        _initializeUpdateAfterAuth(ref);
      }
    });
    return MaterialApp.router(
      routerConfig: router,
      title: EnvInfo.appName,
      theme: AppTheme.lightTheme.copyWith(
        pageTransitionsTheme: const PageTransitionsTheme(
          builders: {
            TargetPlatform.android: CupertinoPageTransitionsBuilder(),
            TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
          },
        ),
      ),
      darkTheme: AppTheme.darkTheme.copyWith(
        pageTransitionsTheme: const PageTransitionsTheme(
          builders: {
            TargetPlatform.android: CupertinoPageTransitionsBuilder(),
            TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
          },
        ),
      ),
      themeMode: themeMode,
      localizationsDelegates: AppLocalizations.localizationsDelegates,
      supportedLocales:
          settingState.value?.localeSupport ?? [const Locale('en', 'US')],
      locale: settingState.value?.locale ?? const Locale('en', 'US'),
      debugShowCheckedModeBanner: false,
      onGenerateTitle: (BuildContext context) => EnvInfo.appName,
    );
  }
}
