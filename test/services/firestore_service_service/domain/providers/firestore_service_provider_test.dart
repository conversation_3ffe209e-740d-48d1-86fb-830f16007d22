import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/services/firestore_service_service/domain/providers/firestore_service_provider.dart';
import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';
import '../../../../mocks/firebase_mocks.dart';
import '../../../../helpers/riverpod_test_utils.dart';

// Additional mock classes for this test
class MockFirebaseFunctions extends Mock implements FirebaseFunctions {}

void main() {
  group('firestoreServiceRepositoryProvider', () {
    late ProviderContainer container;
    late FakeFirebaseFirestore fakeFirestore;
    late MockFirebaseAuth mockFirebaseAuth;
    late MockFirebaseStorage mockFirebaseStorage;
    late MockFirebaseFunctions mockFirebaseFunctions;

    setUp(() {
      // Create fake and mock services
      fakeFirestore = FakeFirebaseFirestore();
      mockFirebaseAuth = MockFirebaseAuth();
      mockFirebaseStorage = MockFirebaseStorage();
      mockFirebaseFunctions = MockFirebaseFunctions();

      // Setup Firebase Auth mocks
      final mockUser = MockUser();
      when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);
      when(() => mockUser.uid).thenReturn('test_user_id');
      when(() => mockUser.email).thenReturn('<EMAIL>');

      // Register fallback values for mocktail
      registerFallbackValue(<String, dynamic>{});
      registerFallbackValue(Uri.parse('https://example.com'));
      registerFallbackValue(const Duration(seconds: 1));
      registerFallbackValue('asia-southeast1');

      // Create container with provider override using test services
      container = ProviderContainer(
        overrides: [
          firestoreServiceRepositoryProvider.overrideWith(
            (ref) => FirestoreServiceRepositoryImpl(
              fakeFirestore,
              mockFirebaseAuth,
              mockFirebaseStorage,
              mockFirebaseFunctions,
            ),
          ),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('Provider Creation', () {
      test('should create FirestoreServiceRepositoryImpl instance', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository, isA<FirestoreServiceRepositoryImpl>());
      });

      test(
        'should return the same instance on multiple reads (singleton behavior)',
        () {
          // Act
          final repository1 = container.read(
            firestoreServiceRepositoryProvider,
          );
          final repository2 = container.read(
            firestoreServiceRepositoryProvider,
          );

          // Assert
          expect(identical(repository1, repository2), isTrue);
        },
      );

      test('should create repository with correct Firebase services', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository.fireStore, equals(fakeFirestore));
        expect(repository.firebaseAuth, equals(mockFirebaseAuth));
        expect(repository.firebaseStorage, equals(mockFirebaseStorage));
        expect(repository.firebaseFunctions, equals(mockFirebaseFunctions));
      });
    });

    group('Firebase Service Initialization', () {
      test('should initialize Firebase Auth service', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository.firebaseAuth, isNotNull);
        expect(repository.firebaseAuth, isA<FirebaseAuth>());
      });

      test('should initialize Firebase Firestore service', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository.fireStore, isNotNull);
        expect(repository.fireStore, isA<FirebaseFirestore>());
      });

      test('should initialize Firebase Storage service', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository.firebaseStorage, isNotNull);
        expect(repository.firebaseStorage, isA<FirebaseStorage>());
      });

      test('should initialize Firebase Functions service', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository.firebaseFunctions, isNotNull);
        expect(repository.firebaseFunctions, isA<FirebaseFunctions>());
      });
    });

    group('Firebase Functions Region Configuration', () {
      test('should configure Firebase Functions with asia-southeast1 region', () {
        // This test verifies that the provider correctly configures the region
        // We can't directly test the region configuration without mocking FirebaseFunctions.instanceFor
        // but we can verify that the provider creates a repository with the expected FirebaseFunctions instance

        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository.firebaseFunctions, isNotNull);
        // The actual region configuration is tested indirectly through the repository's behavior
      });

      test('should handle Firebase Functions initialization correctly', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository.firebaseFunctions, equals(mockFirebaseFunctions));
      });
    });

    group('Repository Interface Implementation', () {
      test('should implement FirestoreServiceRepository interface', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository, isA<FirestoreServiceRepository>());
      });

      test('should provide access to dataUser method', () {
        // Arrange
        final mockUser = MockUser();
        when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(() => mockUser.uid).thenReturn('test_user_id');

        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);
        final documentRef = repository.dataUser();

        // Assert
        expect(documentRef, isNotNull);
        expect(documentRef.path, equals('user-data/test_user_id'));
      });

      test('should handle unauthenticated user in dataUser method', () {
        // Arrange
        when(() => mockFirebaseAuth.currentUser).thenReturn(null);

        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);
        final documentRef = repository.dataUser();

        // Assert
        expect(documentRef, isNotNull);
        expect(documentRef.parent.path, equals('user-data'));
        expect(documentRef.id.isNotEmpty, isTrue);
      });
    });

    group('Provider Dependencies', () {
      test('should not depend on external providers', () {
        // This test verifies that the provider only depends on Firebase services
        // and doesn't require other application-specific providers

        // Act & Assert - No additional setup needed
        expect(
          () => container.read(firestoreServiceRepositoryProvider),
          returnsNormally,
        );
      });

      test('should work with minimal Firebase service setup', () {
        // This test ensures the provider works with just the basic Firebase services

        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository, isNotNull);
        expect(repository.fireStore, isNotNull);
        expect(repository.firebaseAuth, isNotNull);
        expect(repository.firebaseStorage, isNotNull);
        expect(repository.firebaseFunctions, isNotNull);
      });
    });

    group('Error Handling', () {
      test('should handle Firebase service initialization errors gracefully', () {
        // This test would require mocking Firebase service failures
        // For now, we verify that the provider doesn't throw during normal operation

        // Act & Assert
        expect(
          () => container.read(firestoreServiceRepositoryProvider),
          returnsNormally,
        );
      });

      test('should create repository even when Firebase services have issues', () {
        // Arrange - Setup a scenario where Firebase services might have issues
        // (This is a simplified test - in real scenarios, you might mock specific failures)

        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository, isNotNull);
        // The repository should still be created even if individual services have issues
      });
    });

    group('Provider Lifecycle', () {
      test('should properly dispose resources when container is disposed', () {
        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);
        container.dispose();

        // Assert - The repository should still exist but the container should be disposed
        expect(repository, isNotNull);
        expect(
          () => container.read(firestoreServiceRepositoryProvider),
          throwsStateError,
        );
      });

      test('should maintain repository instance across container refresh', () {
        // Act
        final repository1 = container.read(firestoreServiceRepositoryProvider);
        container.refresh(firestoreServiceRepositoryProvider);
        final repository2 = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository1, isNotNull);
        expect(repository2, isNotNull);
        // Note: Riverpod providers create new instances on refresh by default
        // This test documents the expected behavior
      });
    });

    group('Integration with Riverpod Test Utils', () {
      test('should work with RiverpodTestUtils.createTestContainer', () {
        // Act
        final testContainer = RiverpodTestUtils.createTestContainer(
          additionalOverrides: [
            firestoreServiceRepositoryProvider.overrideWith(
              (ref) => FirestoreServiceRepositoryImpl(
                fakeFirestore,
                mockFirebaseAuth,
                mockFirebaseStorage,
                mockFirebaseFunctions,
              ),
            ),
          ],
        );

        // Assert
        expect(
          () => testContainer.read(firestoreServiceRepositoryProvider),
          returnsNormally,
        );

        testContainer.dispose();
      });

      test('should support provider override in tests', () {
        // Act
        final testContainer = ProviderContainer(
          overrides: [
            // Override the provider itself for specific test scenarios
            firestoreServiceRepositoryProvider.overrideWith(
              (ref) => FirestoreServiceRepositoryImpl(
                fakeFirestore,
                mockFirebaseAuth,
                mockFirebaseStorage,
                mockFirebaseFunctions,
              ),
            ),
          ],
        );

        // Assert
        final repository = testContainer.read(
          firestoreServiceRepositoryProvider,
        );
        expect(repository, isA<FirestoreServiceRepositoryImpl>());

        testContainer.dispose();
      });
    });

    group('Mock Integration', () {
      test('should work with direct mock instantiation', () {
        // This test verifies that mocks work correctly when created directly

        // Act
        final authMock = MockFirebaseAuth();
        final firestoreFake = FakeFirebaseFirestore();
        final storageMock = MockFirebaseStorage();
        final functionsMock = MockFirebaseFunctions();

        final testContainer = ProviderContainer(
          overrides: [
            firestoreServiceRepositoryProvider.overrideWith(
              (ref) => FirestoreServiceRepositoryImpl(
                firestoreFake,
                authMock,
                storageMock,
                functionsMock,
              ),
            ),
          ],
        );

        // Assert
        final repository = testContainer.read(
          firestoreServiceRepositoryProvider,
        );
        expect(repository.firebaseAuth, equals(authMock));
        expect(repository.fireStore, equals(firestoreFake));
        expect(repository.firebaseStorage, equals(storageMock));

        testContainer.dispose();
      });

      test('should support direct mock setup for complex test scenarios', () {
        // Arrange
        final mockUser = MockUser();
        when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(() => mockUser.uid).thenReturn('test_user_id');
        when(() => mockUser.email).thenReturn('<EMAIL>');

        // Act
        final repository = container.read(firestoreServiceRepositoryProvider);

        // Assert
        expect(repository.firebaseAuth.currentUser, equals(mockUser));
      });
    });
  });
}
