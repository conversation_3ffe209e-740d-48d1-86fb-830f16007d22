# Testing Guidelines

This document provides comprehensive testing guidelines for the SelfEng Flutter mobile application.


## Testing Framework

This project uses the following testing technologies:

- **Flutter Test SDK**: Core testing framework for unit and widget tests
- **Mocktail**: Mocking library for creating test doubles
- **Fake Cloud Firestore**: In-memory Firestore implementation for testing
- **HTTP Mock Adapter**: HTTP response mocking for API tests
- **Riverpod**: State management testing utilities

## Quick Start

### Running Tests

```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/infrastructure_test.dart

# Run tests by pattern
flutter test --name "UserService"

# Run tests with coverage
flutter test --coverage

# Run tests in specific directory
flutter test test/services/

# Generate and view HTML coverage report
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
open coverage/html/index.html

# Run tests with verbose output
flutter test --reporter expanded
```

### Test Organization

Tests are organized by architectural layer:

```
test/
├── features/                    # Feature-specific tests
│   ├── authentication/         # Auth feature tests
│   ├── dashboard/              # Dashboard tests
│   └── main_lesson/            # Lesson feature tests
├── services/                   # Service layer tests
│   ├── fcm_service/           # FCM service tests
│   ├── user_data_service/     # User data service tests
│   └── firestore_service/     # Firestore service tests
├── shared/                     # Shared domain tests
│   └── domain/models/         # Domain model tests
├── helpers/                    # Testing utilities
│   ├── test_helpers.dart      # General test utilities
│   ├── mock_factories.dart    # Mock creation utilities
│   └── test_data_builders.dart # Test data builders
├── mocks/                      # Centralized mock objects
│   ├── firebase_mocks.dart    # Firebase mocks
│   ├── service_mocks.dart     # Service mocks
│   └── repository_mocks.dart  # Repository mocks
└── examples/                   # Reference implementations
    └── comprehensive_test_example.dart
```

## Testing Standards

### Test Types

**Unit Tests**
- Location: `test/[layer]/[feature]/`
- Purpose: Test individual functions, methods, and classes in isolation
- Naming: `[class_name]_test.dart`

**Widget Tests**
- Location: `test/features/[feature]/widgets/`
- Purpose: Test UI components and user interactions
- Naming: `[widget_name]_test.dart`

**Service Tests**
- Location: `test/services/[service_name]/`
- Purpose: Test service layer business logic
- Naming: `[service_name]_test.dart`

### Coverage Requirements

- **Minimum Overall Coverage**: 80%
- **Critical Components**: 90% (authentication, data models, core business logic)
- **UI Components**: 70%

### Naming Conventions

- **Test files**: `*_test.dart` (e.g., `user_service_test.dart`)
- **Test groups**: Descriptive names explaining the component being tested
- **Test cases**: Should start with "should" and describe expected behavior
- **Mock variables**: Prefix with `mock` (e.g., `mockUserRepository`)
- **Test data**: Use descriptive names (e.g., `testUserData`, `expectedResult`)

### File Organization Rules

- Place tests in the same directory structure as source code
- Group related tests using `group()` blocks
- Keep test files focused on a single class or feature
- Use `setUp()` and `tearDown()` for common initialization and cleanup

## Mock System

### Centralized Mocking

All mocks are centralized to ensure consistency across tests:

- **`test/mocks/firebase_mocks.dart`**: Firebase service mocks
- **`test/mocks/service_mocks.dart`**: Service layer mocks
- **`test/mocks/repository_mocks.dart`**: Repository layer mocks
- **`test/helpers/mock_factories.dart`**: Mock creation utilities

This prevents errors like: “The class 'Query' shouldn't be extended, mixed in, or implemented because it's sealed.”

### Firebase Testing

For Firestore testing, use `fake_cloud_firestore` instead of mocking sealed classes:

```dart
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';

void main() {
  late FakeFirebaseFirestore fakeFirestore;

  setUp(() {
    fakeFirestore = FakeFirebaseFirestore();
  });

  test('should save user data', () async {
    await fakeFirestore.collection('users').doc('123').set({
      'name': 'Test User',
      'email': '<EMAIL>',
    });

    final doc = await fakeFirestore.collection('users').doc('123').get();
    expect(doc.data()?['name'], 'Test User');
  });
}
```

### Using Centralized Mocks

```dart
import 'package:selfeng/test/helpers/mock_factories.dart';

void main() {
  late MockFirebaseAuth mockAuth;
  late MockUser mockUser;

  setUp(() {
    mockAuth = MockFactories.createMockFirebaseAuth();
    mockUser = MockFactories.createMockUser();
    MockFactories.setupFirebaseAuthMocks(mockAuth, mockUser);
  });
}
```

## Writing Tests

### Basic Test Structure

Follow the AAA pattern (Arrange, Act, Assert):

```dart
void main() {
  group('UserService Tests', () {
    late UserService userService;
    late MockUserRepository mockRepository;

    setUp(() {
      mockRepository = MockFactories.createMockUserRepository();
      userService = UserService(mockRepository);
    });

    test('should return user data when repository succeeds', () async {
      // Arrange
      final expectedUser = TestDataBuilders.userDataBuilder().build();
      when(() => mockRepository.getUser('123'))
          .thenAnswer((_) async => Right(expectedUser));

      // Act
      final result = await userService.getUser('123');

      // Assert
      expect(result.isRight(), isTrue);
      expect(result.getOrElse(() => null), expectedUser);
      verify(() => mockRepository.getUser('123')).called(1);
    });
  });
}
```

### Widget Testing

```dart
testWidgets('should display user name when loaded', (tester) async {
  final mockUser = TestDataBuilders.userDataBuilder()
      .withDisplayName('John Doe')
      .build();

  await tester.pumpWidget(
    TestHelpers.createTestWidget(
      child: UserProfileWidget(),
      overrides: [
        userDataProvider.overrideWithValue(mockUser),
      ],
    ),
  );

  expect(find.text('John Doe'), findsOneWidget);
});
```

### Riverpod Provider Testing

```dart
void main() {
  group('UserController Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer(
        overrides: [
          userRepositoryProvider.overrideWithValue(mockUserRepository),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    test('should load user data successfully', () async {
      when(() => mockUserRepository.getUser('123'))
          .thenAnswer((_) async => Right(testUser));

      final controller = container.read(userControllerProvider.notifier);
      await controller.loadUser('123');

      final state = container.read(userControllerProvider);
      expect(state, AsyncData(testUser));
    });
  });
}
```

## Testing Utilities

### Test Data Builders

Use builder pattern for consistent test data:

```dart
final userData = TestDataBuilders.userDataBuilder()
    .withEmail('<EMAIL>')
    .withCompletedTest()
    .withAllLastCourses()
    .build();
```

### Test Helpers

Common utilities for widget and integration testing:

```dart
// Create test widget with providers
TestHelpers.createTestWidget(
  child: MyWidget(),
  overrides: [mockProvider.overrideWithValue(mockValue)],
);

// Wait for async operations
await TestHelpers.waitFor(() => condition);
```

## Best Practices

### Test Organization

- Group related tests using `group()`
- Use descriptive test names starting with "should"
- Follow AAA pattern: Arrange, Act, Assert
- Keep tests focused and independent

### Mock Usage

- Always use centralized mock factories from `test/helpers/mock_factories.dart`
- Verify important method calls with `verify()`
- Reset mocks between tests using `setUp()` and `tearDown()`
- Use `when()` to set up mock behaviors before testing

### Error Testing

```dart
test('should handle network errors gracefully', () async {
  when(() => mockRepository.getData())
      .thenAnswer((_) async => const Left(NetworkException()));

  final result = await service.loadData();

  expect(result.isLeft(), isTrue);
  expect(result.fold((error) => error, (_) => null), isA<NetworkException>());
});
```

### Async Testing

```dart
test('should handle async operations correctly', () async {
  when(() => mockRepository.fetchData())
      .thenAnswer((_) async => Right('success'));

  final result = await service.loadData();

  expect(result.isRight(), isTrue);
});
```

### Stream Testing

```dart
test('should handle stream data correctly', () async {
  final controller = StreamController<String>();
  when(() => mockRepository.watchData())
      .thenAnswer((_) => controller.stream);

  final events = <String>[];
  final subscription = service.watchData().listen(events.add);

  controller.add('test data');
  await Future.delayed(Duration(milliseconds: 10));

  expect(events, contains('test data'));
  await subscription.cancel();
  await controller.close();
});
```

## Common Testing Patterns

### Testing Models with JSON Serialization

```dart
test('should serialize and deserialize correctly', () {
  final original = TestDataBuilders.userDataBuilder().build();
  final json = original.toJson();
  final deserialized = UserData.fromJson(json);

  expect(deserialized, equals(original));
});
```

### Testing Either Pattern

```dart
test('should handle success case with Either', () {
  final result = Either<AppException, String>.right('success');

  final output = result.fold(
    (error) => 'Error: ${error.message}',
    (success) => 'Success: $success',
  );

  expect(output, equals('Success: success'));
});
```

### Testing with Dependency Injection

```dart
test('should create controller with test constructor', () {
  final controller = AuthController.test(
    userRepository: mockUserRepository,
    authRepository: mockAuthRepository,
  );

  expect(controller, isA<AuthController>());
});
```

## Project-Specific Guidelines

### Mobile Platform Focus

This application targets iOS and Android exclusively. All tests are designed for mobile environments and do not support web or desktop platforms.

### Firebase Integration

- Use `fake_cloud_firestore` for Firestore testing instead of mocking sealed classes
- Wrap Firebase services behind repository abstractions for easier testing
- Test Firebase operations using real document paths and data structures

### State Management Testing

This project uses Riverpod for state management:

```dart
test('should update provider state correctly', () async {
  final container = ProviderContainer(
    overrides: [
      repositoryProvider.overrideWithValue(mockRepository),
    ],
  );

  final notifier = container.read(dataProvider.notifier);
  await notifier.loadData();

  final state = container.read(dataProvider);
  expect(state, isA<AsyncData>());

  container.dispose();
});
```

### Error Handling Patterns

The project uses Either pattern for error handling:

```dart
test('should return Left on error', () async {
  when(() => mockRepository.getData())
      .thenAnswer((_) async => Left(NetworkException()));

  final result = await service.getData();

  expect(result.isLeft(), isTrue);
  result.fold(
    (error) => expect(error, isA<NetworkException>()),
    (data) => fail('Expected error but got data'),
  );
});
```

## Quick Reference

### Common Test Patterns Cheat Sheet

```dart
// Basic test structure
test('should do something', () async {
  // Arrange
  when(() => mockRepo.getData()).thenAnswer((_) async => Right(data));

  // Act
  final result = await service.getData();

  // Assert
  expect(result.isRight(), isTrue);
  verify(() => mockRepo.getData()).called(1);
});

// Widget test
testWidgets('should display text', (tester) async {
  await tester.pumpWidget(TestHelpers.createTestWidget(child: MyWidget()));
  expect(find.text('Hello'), findsOneWidget);
});

// Provider test
test('should update state', () async {
  final container = ProviderContainer(overrides: [repo.overrideWithValue(mock)]);
  final notifier = container.read(provider.notifier);
  await notifier.action();
  expect(container.read(provider), expectedState);
  container.dispose();
});
```

### Essential Imports

```dart
// Basic testing
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Widget testing
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project utilities
import 'package:selfeng/test/helpers/mock_factories.dart';
import 'package:selfeng/test/helpers/test_data_builders.dart';
import 'package:selfeng/test/helpers/test_helpers.dart';

// Firebase testing
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
```

## Reference Examples

For comprehensive testing examples, see:
- `test/examples/comprehensive_test_example.dart` - Complete testing patterns
- `test/infrastructure_test.dart` - Basic infrastructure verification
- `test/helpers/` - Testing utilities and mock factories

## Troubleshooting

### Common Issues

**Firestore Sealed Class Errors**
- Use `fake_cloud_firestore` instead of mocking Firestore classes
- Wrap Firestore operations in repository abstractions

**Provider Testing Issues**
- Always dispose ProviderContainer in tearDown()
- Use proper overrides for dependencies

**Mock Setup Problems**
- Use centralized mock factories from `test/helpers/mock_factories.dart`
- Verify mock interactions with `verify()` calls

### Performance Tips

- Keep tests focused and fast (aim for <100ms per test)
- Use setUp() and tearDown() for common initialization
- Reuse mock scenarios across similar tests
- Dispose resources properly to prevent memory leaks
- Avoid unnecessary async operations in tests
- Use `pumpAndSettle()` sparingly in widget tests

## CI/CD Integration

### GitHub Actions / CI Pipeline

```yaml
# Example test step in CI
- name: Run tests
  run: |
    flutter test --coverage --reporter=github

- name: Check coverage
  run: |
    genhtml coverage/lcov.info -o coverage/html
    # Add coverage threshold check here
```

### Pre-commit Hooks

Consider adding these commands to your pre-commit hooks:

```bash
# Run tests before commit
flutter test

# Check test coverage
flutter test --coverage
lcov --summary coverage/lcov.info
```

---

*This document serves as the definitive guide for testing practices in the SelfEng Flutter project. For additional examples and implementation details, refer to the test files in the respective directories.*

